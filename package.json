{"name": "pearleseed-portfolio", "private": true, "version": "1.0.0", "type": "module", "packageManager": "bun@1.2.18", "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "scripts": {"dev": "vite", "dev:clean": "rm -rf node_modules/.vite dist .vite && bun run dev", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "perf": "bun run build && bun run preview --host", "vercel-build": "vite build", "clean": "rm -rf node_modules/.vite dist .vite"}, "dependencies": {"@codemirror/autocomplete": "6.18.6", "@codemirror/commands": "6.8.1", "@codemirror/lang-css": "6.3.1", "@codemirror/lang-html": "6.4.9", "@codemirror/lang-javascript": "6.2.4", "@codemirror/lang-json": "6.0.2", "@codemirror/lang-markdown": "6.3.3", "@codemirror/lang-python": "6.2.1", "@codemirror/language": "6.11.1", "@codemirror/state": "6.5.2", "@codemirror/theme-one-dark": "6.1.3", "@codemirror/view": "6.37.2", "@hookform/resolvers": "5.1.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-tooltip": "1.2.7", "@tanstack/react-query": "5.81.2", "canvas-confetti": "1.9.3", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "framer-motion": "12.18.1", "highlight.js": "^11.11.1", "idb-keyval": "^6.2.2", "ky": "^1.8.1", "limiter": "^3.0.0", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "0.522.0", "nanoid": "^5.1.5", "next-themes": "0.4.6", "p-limit": "^6.2.0", "qrcode.react": "4.2.0", "react": "19.1.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-hook-form": "7.58.1", "react-intersection-observer": "^9.16.0", "react-markdown": "10.1.0", "react-resizable-panels": "3.0.3", "react-router-dom": "7.6.2", "react-scroll-parallax": "3.4.5", "recharts": "2.15.4", "rehype-autolink-headings": "7.1.0", "rehype-highlight": "7.0.2", "rehype-slug": "6.0.0", "remark-gfm": "4.0.1", "sonner": "2.0.5", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "ua-parser-js": "^2.0.4", "use-debounce": "^10.0.5", "validator": "^13.15.15", "zod": "3.25.67"}, "devDependencies": {"@eslint/js": "9.29.0", "@tailwindcss/typography": "0.5.16", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.2.0", "@types/lodash-es": "^4.17.12", "@types/node": "24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/validator": "^13.15.2", "@vitejs/plugin-react-swc": "3.10.2", "autoprefixer": "^10.4.21", "eslint": "9.29.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "globals": "16.2.0", "tailwindcss": "^3.4.17", "typescript": "5.8.3", "typescript-eslint": "8.34.1", "vite": "6.3.5"}}